#!/usr/bin/env python3
"""
Ultra-Advanced GIN Model Optimization for MAE < 0.1 eV
Target: Achieve Mean Absolute Error below 0.1 eV for bandgap prediction
Current best: MAE = 0.2356 eV (need 57% reduction)

Advanced techniques:
1. Ensemble methods (bagging, boosting, stacking)
2. Data augmentation (graph augmentation, noise injection)
3. Cross-validation with hyperparameter optimization
4. Transfer learning and pre-training
5. Advanced regularization techniques
6. Novel architectures and loss functions
"""

import pandas as pd
import numpy as np
import ast
import torch
import torch.nn.functional as F
from torch.nn import Linear, Sequential, BatchNorm1d, ReLU, Dropout, LayerNorm
from torch_geometric.nn import GINConv, global_mean_pool, global_add_pool, global_max_pool
from torch_geometric.data import Data, DataLoader
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.preprocessing import StandardScaler, RobustScaler
import matplotlib.pyplot as plt
import seaborn as sns
from pymatgen.core import Element
import warnings
warnings.filterwarnings('ignore')

# Set seeds for reproducibility
torch.manual_seed(42)
np.random.seed(42)

print("=" * 80)
print("🚀 ULTRA-ADVANCED GIN OPTIMIZATION FOR MAE < 0.1 eV")
print("=" * 80)

print("\n🎯 OPTIMIZATION TARGET:")
print("• Current best MAE: 0.2356 eV")
print("• Target MAE: < 0.1 eV")
print("• Required improvement: 57% reduction")
print("• Maintain: RMSE < 0.5 eV, R² > 0")

# Load and prepare data
print("\n=== LOADING AND PREPARING DATA ===")
data = pd.read_csv('/home/<USER>/Coding/Binary chalcogenides/Graph structure dataset for dichalcogenides (2).csv')
print(f"Dataset shape: {data.shape}")

# Enhanced feature engineering
def get_enhanced_element_properties(element_symbol):
    """Get comprehensive element properties"""
    try:
        element = Element(element_symbol)
        return [
            element.atomic_radius or 1.0,
            element.atomic_mass,
            element.X or 1.5,  # electronegativity
            element.group,
            element.row,
            element.atomic_orbitals.get('s', 0) + element.atomic_orbitals.get('p', 0) + element.atomic_orbitals.get('d', 0),
            element.metallic_radius or element.atomic_radius or 1.0,
            element.van_der_waals_radius or element.atomic_radius or 1.0,
            element.average_ionic_radius or 1.0,
            element.density_of_solid or 1.0,
            element.melting_point or 1000.0,
            element.boiling_point or 2000.0,
            element.thermal_conductivity or 1.0,
            element.electrical_resistivity or 1.0,
            element.bulk_modulus or 1.0
        ]
    except:
        return [1.0] * 15

# Process data with enhanced features
unique_elements = set()
for elements_str in data['Node Elements']:
    elements = ast.literal_eval(elements_str)
    unique_elements.update(elements)

print(f"Unique elements: {len(unique_elements)}")

# Create enhanced element property matrix
element_properties = {}
for element in unique_elements:
    element_properties[element] = get_enhanced_element_properties(element)

# Convert to numpy array for normalization
all_props = np.array(list(element_properties.values()))
prop_scaler = RobustScaler()
normalized_props = prop_scaler.fit_transform(all_props)

# Update element properties with normalized values
for i, element in enumerate(unique_elements):
    element_properties[element] = normalized_props[i]

print(f"Enhanced node features dimension: {len(normalized_props[0])}")

# Create enhanced graphs with augmentation
def create_enhanced_graph_with_augmentation(row, augment=False):
    """Create graph with optional augmentation"""
    node_elements = ast.literal_eval(row['Node Elements'])
    adj_matrix = np.array(ast.literal_eval(row['Adjacency Matrix']))

    # Enhanced node features
    node_features = []
    for element in node_elements:
        coords = [row['a'], row['b'], row['c']]  # Crystal parameters
        element_props = element_properties[element]

        # Combine coordinates and element properties
        features = coords + list(element_props)

        # Data augmentation
        if augment:
            # Add small random noise
            noise = np.random.normal(0, 0.01, len(features))
            features = np.array(features) + noise

        node_features.append(features)

    X = torch.tensor(node_features, dtype=torch.float32)
    num_nodes = X.shape[0]

    # Create edge indices from adjacency matrix - fix indexing issue
    edge_indices = []
    adj_matrix = adj_matrix[:num_nodes, :num_nodes]  # Ensure matrix matches node count

    for i in range(min(len(adj_matrix), num_nodes)):
        for j in range(min(len(adj_matrix[i]), num_nodes)):
            if i < num_nodes and j < num_nodes and adj_matrix[i][j] == 1:
                edge_indices.append([i, j])

    # Ensure we have at least one edge
    if not edge_indices:
        # Create self-loops for all nodes
        edge_indices = [[i, i] for i in range(num_nodes)]

    edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()

    # Validate edge indices
    max_node_idx = edge_index.max().item() if edge_index.numel() > 0 else 0
    if max_node_idx >= num_nodes:
        print(f"Warning: Edge index {max_node_idx} >= num_nodes {num_nodes}, creating self-loops")
        edge_indices = [[i, i] for i in range(num_nodes)]
        edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()

    # Target with optional noise for regularization
    target = row['Bandgap (eV)']
    if augment and target > 0:  # Only augment non-zero targets
        target += np.random.normal(0, 0.005)  # Small noise
        target = max(0, target)  # Ensure non-negative

    y = torch.tensor([target], dtype=torch.float32)

    return Data(x=X, edge_index=edge_index, y=y)

# Create base dataset
print("Creating enhanced graphs...")
graphs = []
for idx, row in data.iterrows():
    graph = create_enhanced_graph_with_augmentation(row, augment=False)
    graphs.append(graph)

# Data augmentation - create additional samples
print("Applying data augmentation...")
augmented_graphs = []
for idx, row in data.iterrows():
    # Create 3 augmented versions of each sample
    for _ in range(3):
        aug_graph = create_enhanced_graph_with_augmentation(row, augment=True)
        augmented_graphs.append(aug_graph)

# Combine original and augmented data
all_graphs = graphs + augmented_graphs
print(f"Total graphs after augmentation: {len(all_graphs)} (original: {len(graphs)})")

actual_feature_dim = all_graphs[0].x.shape[1]
print(f"Enhanced feature dimension: {actual_feature_dim}")

# Ultra-Advanced GIN Architecture
class UltraAdvancedGIN(torch.nn.Module):
    """Ultra-advanced GIN with ensemble-ready architecture"""
    def __init__(self, input_dim, dim_h, num_layers=3, dropout_rate=0.3, 
                 use_residual=True, use_attention=True, use_batch_norm=True):
        super(UltraAdvancedGIN, self).__init__()
        self.num_layers = num_layers
        self.dropout_rate = dropout_rate
        self.use_residual = use_residual
        self.use_attention = use_attention
        
        # Input projection with multiple pathways
        self.input_proj1 = Linear(input_dim, dim_h)
        self.input_proj2 = Linear(input_dim, dim_h)
        self.input_bn = BatchNorm1d(dim_h) if use_batch_norm else torch.nn.Identity()
        
        # Multi-scale GIN layers
        self.convs = torch.nn.ModuleList()
        self.batch_norms = torch.nn.ModuleList()
        self.layer_norms = torch.nn.ModuleList()
        
        for i in range(num_layers):
            # Ultra-deep MLP within GIN
            mlp = Sequential(
                Linear(dim_h, dim_h * 2),
                BatchNorm1d(dim_h * 2) if use_batch_norm else torch.nn.Identity(),
                ReLU(),
                Dropout(dropout_rate * 0.5),
                Linear(dim_h * 2, dim_h * 3),
                BatchNorm1d(dim_h * 3) if use_batch_norm else torch.nn.Identity(),
                ReLU(),
                Dropout(dropout_rate * 0.3),
                Linear(dim_h * 3, dim_h * 2),
                BatchNorm1d(dim_h * 2) if use_batch_norm else torch.nn.Identity(),
                ReLU(),
                Linear(dim_h * 2, dim_h)
            )
            
            self.convs.append(GINConv(mlp))
            self.batch_norms.append(BatchNorm1d(dim_h) if use_batch_norm else torch.nn.Identity())
            self.layer_norms.append(LayerNorm(dim_h))
        
        # Multi-head attention
        if use_attention:
            self.attention = torch.nn.MultiheadAttention(dim_h, num_heads=8, batch_first=True, dropout=dropout_rate)
            self.attention_norm = LayerNorm(dim_h)
        
        # Ultra-sophisticated readout
        readout_dim = dim_h * num_layers
        self.readout = Sequential(
            Linear(readout_dim, readout_dim),
            BatchNorm1d(readout_dim) if use_batch_norm else torch.nn.Identity(),
            ReLU(),
            Dropout(dropout_rate),
            Linear(readout_dim, readout_dim // 2),
            BatchNorm1d(readout_dim // 2) if use_batch_norm else torch.nn.Identity(),
            ReLU(),
            Dropout(dropout_rate * 0.7),
            Linear(readout_dim // 2, readout_dim // 4),
            BatchNorm1d(readout_dim // 4) if use_batch_norm else torch.nn.Identity(),
            ReLU(),
            Dropout(dropout_rate * 0.5),
            Linear(readout_dim // 4, readout_dim // 8),
            ReLU(),
            Linear(readout_dim // 8, 1)
        )
        
        self._init_weights()
    
    def _init_weights(self):
        """Advanced weight initialization"""
        for m in self.modules():
            if isinstance(m, Linear):
                if m.out_features == 1:  # Output layer
                    torch.nn.init.xavier_normal_(m.weight, gain=0.01)
                else:
                    torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    torch.nn.init.zeros_(m.bias)
    
    def forward(self, x, edge_index, batch):
        # Dual input pathways
        h1 = F.relu(self.input_bn(self.input_proj1(x)))
        h2 = F.tanh(self.input_bn(self.input_proj2(x)))
        h = h1 + h2  # Combine pathways
        
        layer_outputs = []
        
        for i, (conv, bn, ln) in enumerate(zip(self.convs, self.batch_norms, self.layer_norms)):
            h_new = conv(h, edge_index)
            h_new = bn(h_new)
            
            # Residual connections
            if self.use_residual and i > 0 and h.shape == h_new.shape:
                h = h + h_new
            else:
                h = h_new
            
            h = ln(h)
            h = F.dropout(h, p=self.dropout_rate * 0.6, training=self.training)
            layer_outputs.append(h)
        
        # Attention mechanism
        if self.use_attention and len(layer_outputs) > 1:
            stacked = torch.stack(layer_outputs, dim=1)
            attended, _ = self.attention(stacked, stacked, stacked)
            attended = self.attention_norm(attended + stacked)
            layer_outputs = [attended[:, i, :] for i in range(attended.shape[1])]
        
        # Multi-scale pooling
        pooled_outputs = []
        for h in layer_outputs:
            mean_pool = global_mean_pool(h, batch)
            add_pool = global_add_pool(h, batch)
            max_pool = global_max_pool(h, batch)
            # Combine different pooling strategies
            combined = (mean_pool + add_pool + max_pool) / 3
            pooled_outputs.append(combined)
        
        graph_repr = torch.cat(pooled_outputs, dim=1)
        output = self.readout(graph_repr)
        
        return output.squeeze()

print("Ultra-advanced GIN architecture defined!")

# Ensemble Methods
class EnsembleGIN:
    """Ensemble of multiple GIN models"""
    def __init__(self, input_dim, num_models=5):
        self.models = []
        self.num_models = num_models

        # Create diverse models
        configs = [
            {'dim_h': 64, 'num_layers': 3, 'dropout_rate': 0.2},
            {'dim_h': 48, 'num_layers': 4, 'dropout_rate': 0.3},
            {'dim_h': 80, 'num_layers': 2, 'dropout_rate': 0.25},
            {'dim_h': 56, 'num_layers': 3, 'dropout_rate': 0.35},
            {'dim_h': 72, 'num_layers': 3, 'dropout_rate': 0.28}
        ]

        for i in range(num_models):
            config = configs[i % len(configs)]
            model = UltraAdvancedGIN(
                input_dim=input_dim,
                dim_h=config['dim_h'],
                num_layers=config['num_layers'],
                dropout_rate=config['dropout_rate']
            )
            self.models.append(model)

    def train_ensemble(self, train_loader, val_loader, epochs=200):
        """Train all models in ensemble"""
        trained_models = []

        for i, model in enumerate(self.models):
            print(f"\nTraining ensemble model {i+1}/{self.num_models}...")

            optimizer = torch.optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-5)
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
            criterion = torch.nn.MSELoss()

            best_val_loss = float('inf')
            patience = 30
            patience_counter = 0

            for epoch in range(epochs):
                # Training
                model.train()
                train_loss = 0
                for data in train_loader:
                    optimizer.zero_grad()
                    out = model(data.x, data.edge_index, data.batch)
                    loss = criterion(out, data.y)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                    optimizer.step()
                    train_loss += loss.item()

                # Validation
                model.eval()
                val_loss = 0
                with torch.no_grad():
                    for data in val_loader:
                        out = model(data.x, data.edge_index, data.batch)
                        loss = criterion(out, data.y)
                        val_loss += loss.item()

                val_loss /= len(val_loader)
                scheduler.step()

                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1

                if patience_counter >= patience:
                    print(f"Early stopping at epoch {epoch}")
                    break

                if epoch % 50 == 0:
                    print(f"Epoch {epoch}, Train: {train_loss/len(train_loader):.4f}, Val: {val_loss:.4f}")

            trained_models.append(model)

        self.models = trained_models
        return self.models

    def predict(self, test_loader):
        """Make ensemble predictions"""
        all_predictions = []

        for model in self.models:
            model.eval()
            predictions = []
            with torch.no_grad():
                for data in test_loader:
                    out = model(data.x, data.edge_index, data.batch)
                    predictions.extend(out.cpu().numpy())
            all_predictions.append(predictions)

        # Average predictions
        ensemble_predictions = np.mean(all_predictions, axis=0)
        return ensemble_predictions

# Advanced Cross-Validation with Hyperparameter Optimization
def advanced_cross_validation(graphs, n_splits=5):
    """Perform advanced cross-validation"""
    print(f"\n=== ADVANCED {n_splits}-FOLD CROSS-VALIDATION ===")

    # Stratified split based on bandgap ranges
    targets = [graph.y.item() for graph in graphs]

    # Create bins for stratification
    bins = np.percentile(targets, [0, 20, 40, 60, 80, 100])
    bin_indices = np.digitize(targets, bins) - 1

    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)

    cv_results = []

    for fold, (train_idx, val_idx) in enumerate(skf.split(graphs, bin_indices)):
        print(f"\n--- FOLD {fold + 1}/{n_splits} ---")

        train_graphs = [graphs[i] for i in train_idx]
        val_graphs = [graphs[i] for i in val_idx]

        train_loader = DataLoader(train_graphs, batch_size=32, shuffle=True)
        val_loader = DataLoader(val_graphs, batch_size=32, shuffle=False)

        # Train ensemble for this fold
        ensemble = EnsembleGIN(input_dim=actual_feature_dim, num_models=3)
        ensemble.train_ensemble(train_loader, val_loader, epochs=150)

        # Evaluate
        predictions = ensemble.predict(val_loader)
        targets = [graph.y.item() for graph in val_graphs]

        mae = mean_absolute_error(targets, predictions)
        rmse = np.sqrt(mean_squared_error(targets, predictions))
        r2 = r2_score(targets, predictions)

        print(f"Fold {fold + 1} Results: MAE={mae:.4f}, RMSE={rmse:.4f}, R²={r2:.4f}")

        cv_results.append({
            'fold': fold + 1,
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'ensemble': ensemble
        })

    # Calculate average performance
    avg_mae = np.mean([r['mae'] for r in cv_results])
    avg_rmse = np.mean([r['rmse'] for r in cv_results])
    avg_r2 = np.mean([r['r2'] for r in cv_results])

    print(f"\n🎯 CROSS-VALIDATION RESULTS:")
    print(f"Average MAE: {avg_mae:.4f} ± {np.std([r['mae'] for r in cv_results]):.4f}")
    print(f"Average RMSE: {avg_rmse:.4f} ± {np.std([r['rmse'] for r in cv_results]):.4f}")
    print(f"Average R²: {avg_r2:.4f} ± {np.std([r['r2'] for r in cv_results]):.4f}")

    return cv_results, avg_mae, avg_rmse, avg_r2

# Ultra-Advanced Training with Multiple Techniques
def ultra_advanced_training():
    """Main training function with all advanced techniques"""
    print("\n=== ULTRA-ADVANCED TRAINING PIPELINE ===")

    # Split data
    split_idx = int(0.8 * len(all_graphs))
    train_graphs = all_graphs[:split_idx]
    test_graphs = all_graphs[split_idx:]

    print(f"Training graphs: {len(train_graphs)}")
    print(f"Test graphs: {len(test_graphs)}")

    # Cross-validation
    cv_results, avg_mae, avg_rmse, avg_r2 = advanced_cross_validation(train_graphs)

    # Final ensemble training on full training set
    print(f"\n=== FINAL ENSEMBLE TRAINING ===")
    train_loader = DataLoader(train_graphs, batch_size=24, shuffle=True)
    test_loader = DataLoader(test_graphs, batch_size=24, shuffle=False)

    # Create and train final ensemble
    final_ensemble = EnsembleGIN(input_dim=actual_feature_dim, num_models=7)
    final_ensemble.train_ensemble(train_loader, test_loader, epochs=300)

    # Final evaluation
    print(f"\n=== FINAL EVALUATION ===")
    final_predictions = final_ensemble.predict(test_loader)
    final_targets = [graph.y.item() for graph in test_graphs]

    final_mae = mean_absolute_error(final_targets, final_predictions)
    final_rmse = np.sqrt(mean_squared_error(final_targets, final_predictions))
    final_r2 = r2_score(final_targets, final_predictions)

    print(f"\n🏆 FINAL RESULTS:")
    print(f"Final MAE: {final_mae:.4f} eV")
    print(f"Final RMSE: {final_rmse:.4f} eV")
    print(f"Final R²: {final_r2:.4f}")

    # Check if target achieved
    target_achieved = final_mae < 0.1
    print(f"\n🎯 TARGET ACHIEVEMENT:")
    print(f"MAE < 0.1 eV: {'✅ ACHIEVED' if target_achieved else '❌ NOT ACHIEVED'}")
    print(f"RMSE < 0.5 eV: {'✅ ACHIEVED' if final_rmse < 0.5 else '❌ NOT ACHIEVED'}")
    print(f"R² > 0: {'✅ ACHIEVED' if final_r2 > 0 else '❌ NOT ACHIEVED'}")

    return {
        'cv_mae': avg_mae,
        'cv_rmse': avg_rmse,
        'cv_r2': avg_r2,
        'final_mae': final_mae,
        'final_rmse': final_rmse,
        'final_r2': final_r2,
        'target_achieved': target_achieved,
        'predictions': final_predictions,
        'targets': final_targets,
        'ensemble': final_ensemble
    }

# Run the ultra-advanced optimization
if __name__ == "__main__":
    results = ultra_advanced_training()

    # Create final analysis
    print(f"\n" + "=" * 80)
    print("🚀 ULTRA-ADVANCED OPTIMIZATION COMPLETE")
    print("=" * 80)

    if results['target_achieved']:
        print("🎉 SUCCESS: MAE < 0.1 eV TARGET ACHIEVED!")
        print(f"Final MAE: {results['final_mae']:.4f} eV")
        improvement = ((0.2356 - results['final_mae']) / 0.2356) * 100
        print(f"Improvement over previous best: {improvement:.1f}%")
    else:
        print("❌ TARGET NOT ACHIEVED")
        print(f"Final MAE: {results['final_mae']:.4f} eV")
        print(f"Gap to target: {results['final_mae'] - 0.1:.4f} eV")

        print(f"\n🔍 ANALYSIS OF LIMITATIONS:")
        print(f"• Dataset size: {len(data)} materials (may be insufficient)")
        print(f"• Feature complexity: {actual_feature_dim} features per node")
        print(f"• Target distribution: {np.std([g.y.item() for g in all_graphs]):.3f} eV std")
        print(f"• Theoretical limit may be reached with current data")

    print(f"\n📊 COMPREHENSIVE RESULTS:")
    print(f"Cross-validation MAE: {results['cv_mae']:.4f} eV")
    print(f"Final test MAE: {results['final_mae']:.4f} eV")
    print(f"Final test RMSE: {results['final_rmse']:.4f} eV")
    print(f"Final test R²: {results['final_r2']:.4f}")

    print(f"\n✨ OPTIMIZATION COMPLETED!")
