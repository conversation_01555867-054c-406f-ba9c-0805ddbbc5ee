api = 'h79cBkPf4hpT24odYyHNpGTRNWuuPh2E'
from mp_api.client import MPRester


def get_adjacency_matrix(structure):
    """
    Returns elements and atomic positions as a dictionary keyed by site index,
    along with the adjacency matrix.
    """
    from pymatgen.analysis.local_env import CrystalNN
    import numpy as np
    with MPRester(api) as mpr:
        cnn = CrystalNN()
        n_sites = len(structure)
        
        # Initialize adjacency matrix
        adjacency_matrix = np.zeros((n_sites, n_sites), dtype=int)
        
        # Fill adjacency matrix
        for i in range(n_sites):
            neighbors = cnn.get_nn_info(structure, i)
            for neighbor in neighbors:
                j = neighbor['site_index']
                adjacency_matrix[i, j] = 1
        
        # Create a dict with site index as key and a tuple (element, position) as value
        elements_positions = {
            idx: (site.species_string, tuple(site.coords))
            for idx, site in enumerate(structure)
        }
        
    return elements_positions, adjacency_matrix
