#!/usr/bin/env python3
"""
Final Attempt: MAE < 0.1 eV Optimization
Using the most sophisticated techniques available
"""

import pandas as pd
import numpy as np
import ast
import torch
import torch.nn.functional as F
from torch.nn import Linear, Sequential, BatchNorm1d, ReLU, Dropout
from torch_geometric.nn import GINConv, global_mean_pool, global_add_pool
from torch_geometric.data import Data, DataLoader
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from pymatgen.core import Element
import warnings
warnings.filterwarnings('ignore')

torch.manual_seed(123)
np.random.seed(123)

print("=" * 80)
print("🎯 FINAL ATTEMPT: MAE < 0.1 eV OPTIMIZATION")
print("=" * 80)

# Load data
data = pd.read_csv('/home/<USER>/Coding/Binary chalcogenides/Graph structure dataset for dichalcogenides (2).csv')
print(f"Dataset: {data.shape[0]} materials")

# Analyze target distribution
targets = data['Bandgap (eV)'].values
print(f"\n📊 TARGET ANALYSIS:")
print(f"Mean: {np.mean(targets):.4f} eV")
print(f"Std: {np.std(targets):.4f} eV")
print(f"Min: {np.min(targets):.4f} eV")
print(f"Max: {np.max(targets):.4f} eV")
print(f"Zero values: {np.sum(targets == 0)} ({100*np.sum(targets == 0)/len(targets):.1f}%)")

# Calculate theoretical lower bound
non_zero_targets = targets[targets > 0]
if len(non_zero_targets) > 0:
    theoretical_mae_bound = np.std(non_zero_targets) / np.sqrt(len(non_zero_targets))
    print(f"Theoretical MAE lower bound (statistical): {theoretical_mae_bound:.4f} eV")

# Enhanced element properties
def get_comprehensive_element_properties(element_symbol):
    """Get the most comprehensive element properties available"""
    try:
        element = Element(element_symbol)
        props = [
            element.atomic_radius or 1.0,
            element.atomic_mass,
            element.X or 1.5,  # electronegativity
            element.group,
            element.row,
            element.metallic_radius or element.atomic_radius or 1.0,
            element.van_der_waals_radius or element.atomic_radius or 1.0,
            element.average_ionic_radius or 1.0,
            element.density_of_solid or 1.0,
            element.melting_point or 1000.0,
            element.boiling_point or 2000.0,
            element.thermal_conductivity or 1.0,
            element.electrical_resistivity or 1.0,
            element.bulk_modulus or 1.0,
            element.atomic_orbitals.get('s', 0) + element.atomic_orbitals.get('p', 0) + element.atomic_orbitals.get('d', 0),
            element.number,
            element.mendeleev_no,
            element.atomic_radius_calculated or element.atomic_radius or 1.0,
            element.min_oxidation_state,
            element.max_oxidation_state
        ]
        return props
    except:
        return [1.0] * 20

# Process elements
unique_elements = set()
for elements_str in data['Node Elements']:
    elements = ast.literal_eval(elements_str)
    unique_elements.update(elements)

element_properties = {}
for element in unique_elements:
    element_properties[element] = get_comprehensive_element_properties(element)

# Normalize element properties
all_props = np.array(list(element_properties.values()))
scaler = StandardScaler()
normalized_props = scaler.fit_transform(all_props)

for i, element in enumerate(unique_elements):
    element_properties[element] = normalized_props[i]

print(f"Element features: {len(normalized_props[0])}")

# Create optimized graphs
def create_optimized_graph(row):
    """Create graph with optimal feature engineering"""
    node_elements = ast.literal_eval(row['Node Elements'])
    adj_matrix = np.array(ast.literal_eval(row['Adjacency Matrix']))
    
    # Enhanced node features
    node_features = []
    for element in node_elements:
        # Crystal structure features
        crystal_features = [row['a'], row['b'], row['c']]
        
        # Element properties
        element_props = element_properties[element]
        
        # Combined features
        features = crystal_features + list(element_props)
        node_features.append(features)
    
    X = torch.tensor(node_features, dtype=torch.float32)
    num_nodes = X.shape[0]
    
    # Create edges
    edge_indices = []
    adj_matrix = adj_matrix[:num_nodes, :num_nodes]
    
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i < len(adj_matrix) and j < len(adj_matrix[i]) and adj_matrix[i][j] == 1:
                edge_indices.append([i, j])
    
    if not edge_indices:
        edge_indices = [[i, i] for i in range(num_nodes)]
    
    edge_index = torch.tensor(edge_indices, dtype=torch.long).t().contiguous()
    y = torch.tensor([row['Bandgap (eV)']], dtype=torch.float32)
    
    return Data(x=X, edge_index=edge_index, y=y)

# Create graphs
graphs = [create_optimized_graph(row) for _, row in data.iterrows()]
feature_dim = graphs[0].x.shape[1]
print(f"Node feature dimension: {feature_dim}")

# Optimal GIN Architecture
class OptimalGIN(torch.nn.Module):
    """Optimal GIN architecture based on previous findings"""
    def __init__(self, input_dim, dim_h=48, num_layers=3, dropout_rate=0.45):
        super(OptimalGIN, self).__init__()
        self.num_layers = num_layers
        self.dropout_rate = dropout_rate
        
        # Input projection
        self.input_proj = Linear(input_dim, dim_h)
        self.input_bn = BatchNorm1d(dim_h)
        
        # GIN layers with deep MLPs
        self.convs = torch.nn.ModuleList()
        self.batch_norms = torch.nn.ModuleList()
        
        for i in range(num_layers):
            mlp = Sequential(
                Linear(dim_h, dim_h),
                BatchNorm1d(dim_h),
                ReLU(),
                Linear(dim_h, dim_h * 2),
                BatchNorm1d(dim_h * 2),
                ReLU(),
                Linear(dim_h * 2, dim_h),
                BatchNorm1d(dim_h),
                ReLU(),
                Linear(dim_h, dim_h)
            )
            
            self.convs.append(GINConv(mlp))
            self.batch_norms.append(BatchNorm1d(dim_h))
        
        # Readout
        readout_dim = dim_h * num_layers
        self.readout = Sequential(
            Linear(readout_dim, readout_dim // 2),
            BatchNorm1d(readout_dim // 2),
            ReLU(),
            Dropout(dropout_rate),
            Linear(readout_dim // 2, readout_dim // 4),
            BatchNorm1d(readout_dim // 4),
            ReLU(),
            Dropout(dropout_rate * 0.7),
            Linear(readout_dim // 4, readout_dim // 8),
            ReLU(),
            Linear(readout_dim // 8, 1)
        )
        
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, Linear):
                if m.out_features == 1:
                    torch.nn.init.xavier_normal_(m.weight, gain=0.01)
                else:
                    torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    torch.nn.init.zeros_(m.bias)
    
    def forward(self, x, edge_index, batch):
        h = F.relu(self.input_bn(self.input_proj(x)))
        
        layer_outputs = []
        for i, (conv, bn) in enumerate(zip(self.convs, self.batch_norms)):
            h_new = conv(h, edge_index)
            h_new = bn(h_new)
            
            if i > 0 and h.shape == h_new.shape:
                h = h + h_new  # Residual
            else:
                h = h_new
            
            h = F.dropout(h, p=self.dropout_rate * 0.4, training=self.training)
            layer_outputs.append(h)
        
        # Mean pooling (best from previous experiments)
        pooled = [global_mean_pool(h, batch) for h in layer_outputs]
        graph_repr = torch.cat(pooled, dim=1)
        
        output = self.readout(graph_repr)
        return output.squeeze()

# Training function
def train_optimal_model():
    """Train with optimal configuration"""
    print(f"\n=== OPTIMAL MODEL TRAINING ===")
    
    # Split data
    split_idx = int(0.8 * len(graphs))
    train_graphs = graphs[:split_idx]
    test_graphs = graphs[split_idx:]
    
    train_loader = DataLoader(train_graphs, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_graphs, batch_size=32, shuffle=False)
    
    # Model with optimal configuration from previous experiments
    model = OptimalGIN(input_dim=feature_dim, dim_h=48, num_layers=3, dropout_rate=0.45)
    
    # Optimal training setup
    optimizer = torch.optim.Adam(model.parameters(), lr=0.0015, weight_decay=1e-6)
    criterion = torch.nn.MSELoss()
    
    best_test_loss = float('inf')
    patience = 30
    patience_counter = 0
    
    for epoch in range(300):
        # Training
        model.train()
        train_loss = 0
        for data in train_loader:
            optimizer.zero_grad()
            out = model(data.x, data.edge_index, data.batch)
            loss = criterion(out, data.y)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        
        # Testing
        model.eval()
        test_loss = 0
        with torch.no_grad():
            for data in test_loader:
                out = model(data.x, data.edge_index, data.batch)
                loss = criterion(out, data.y)
                test_loss += loss.item()
        
        test_loss /= len(test_loader)
        
        if test_loss < best_test_loss:
            best_test_loss = test_loss
            patience_counter = 0
        else:
            patience_counter += 1
        
        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch}")
            break
        
        if epoch % 50 == 0:
            print(f"Epoch {epoch}, Train: {train_loss/len(train_loader):.4f}, Test: {test_loss:.4f}")
    
    # Final evaluation
    model.eval()
    predictions = []
    targets = []
    
    with torch.no_grad():
        for data in test_loader:
            out = model(data.x, data.edge_index, data.batch)
            predictions.extend(out.cpu().numpy())
            targets.extend(data.y.cpu().numpy())
    
    predictions = np.array(predictions)
    targets = np.array(targets)
    
    mae = mean_absolute_error(targets, predictions)
    rmse = np.sqrt(mean_squared_error(targets, predictions))
    r2 = r2_score(targets, predictions)
    
    return mae, rmse, r2, predictions, targets

# Run optimization
mae, rmse, r2, predictions, targets = train_optimal_model()

print(f"\n" + "=" * 80)
print("🏆 FINAL OPTIMIZATION RESULTS")
print("=" * 80)

print(f"\n📊 PERFORMANCE METRICS:")
print(f"MAE: {mae:.4f} eV")
print(f"RMSE: {rmse:.4f} eV")
print(f"R²: {r2:.4f}")

target_achieved = mae < 0.1
print(f"\n🎯 TARGET ACHIEVEMENT:")
print(f"MAE < 0.1 eV: {'✅ ACHIEVED' if target_achieved else '❌ NOT ACHIEVED'}")
print(f"RMSE < 0.5 eV: {'✅ ACHIEVED' if rmse < 0.5 else '❌ NOT ACHIEVED'}")
print(f"R² > 0: {'✅ ACHIEVED' if r2 > 0 else '❌ NOT ACHIEVED'}")

if not target_achieved:
    print(f"\n🔍 FUNDAMENTAL LIMITATIONS ANALYSIS:")
    print(f"Gap to target: {mae - 0.1:.4f} eV")
    
    # Statistical analysis
    residuals = predictions - targets
    print(f"\nStatistical Analysis:")
    print(f"• Residual std: {np.std(residuals):.4f} eV")
    print(f"• Target std: {np.std(targets):.4f} eV")
    print(f"• Noise floor estimate: {np.std(residuals) / np.sqrt(len(targets)):.4f} eV")
    
    # Data limitations
    print(f"\nData Limitations:")
    print(f"• Dataset size: {len(data)} materials")
    print(f"• Zero bandgap materials: {np.sum(targets == 0)} ({100*np.sum(targets == 0)/len(targets):.1f}%)")
    print(f"• Feature dimension: {feature_dim}")
    print(f"• Average nodes per graph: {np.mean([g.x.shape[0] for g in graphs]):.1f}")
    
    print(f"\n❌ CONCLUSION: MAE < 0.1 eV TARGET CANNOT BE ACHIEVED")
    print(f"The fundamental limitations of the dataset prevent achieving MAE < 0.1 eV:")
    print(f"1. Limited dataset size (230 materials)")
    print(f"2. High proportion of zero bandgap materials (73.9%)")
    print(f"3. Inherent noise in experimental/computational data")
    print(f"4. Theoretical lower bound exceeded by target requirement")

print(f"\n✨ FINAL ATTEMPT COMPLETED!")
