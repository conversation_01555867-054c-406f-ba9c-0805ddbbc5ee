import pandas as pd
import numpy as np
import ast
import torch
import torch.nn.functional as F
from torch.nn import Linear, Sequential, BatchNorm1d, ReLU, Dropout
from torch_geometric.nn import GINConv, global_mean_pool, global_add_pool
from torch_geometric.data import Data, DataLoader
import matplotlib.pyplot as plt
import networkx as nx
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Set random seeds for reproducibility
torch.manual_seed(11)
torch.cuda.manual_seed(0)
torch.cuda.manual_seed_all(0)
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False

print("=== Loading and Processing Data ===")

# Load the dataset
data = pd.read_csv('/home/<USER>/Coding/Binary chalcogenides/Graph structure dataset for dichalcogenides (2).csv')
print(f"Dataset shape: {data.shape}")
print(f"Columns: {data.columns.tolist()}")

# Extract unique elements from the dataset
all_elements = []
for d in data['Node Elements']:
    d_dict = dict(ast.literal_eval(d))
    all_elements.extend(d_dict.keys())

unique_elements = set(all_elements)
print(f"Unique elements: {unique_elements}")
print(f"Number of unique elements: {len(unique_elements)}")

# Function to get element properties
def get_element_properties(element_symbol):
    try:
        from pymatgen.core import Element
        element = Element(element_symbol)
        return {
            'Element': element_symbol,
            'atomic_number': element.Z,
            'atomic_mass': element.atomic_mass,
            'atomic_radius': element.atomic_radius or 0,
            'electronegativity': element.X or 0,
            'ionization_energy': element.ionization_energy or 0,
            'electron_affinity': element.electron_affinity or 0,
            'melting_point': element.melting_point or 0
        }
    except:
        return {
            'Element': element_symbol,
            'atomic_number': 0,
            'atomic_mass': 0,
            'atomic_radius': 0,
            'electronegativity': 0,
            'ionization_energy': 0,
            'electron_affinity': 0,
            'melting_point': 0
        }

# Create element properties dataset
elements_list = []
for element in unique_elements:
    elements_list.append(get_element_properties(element))

elements_data = pd.DataFrame(elements_list).fillna(0)
print(f"Element properties shape: {elements_data.shape}")

# Function to return element row
def return_elem_row(name):
    row = elements_data[elements_data['Element'] == name].select_dtypes(np.number).values.flatten().tolist()
    return row

# Create graph node dataset
Graph_node_dataset = []
for row in data['Node Elements']:
    material = []
    row = dict(ast.literal_eval(row))
    for elem, value in row.items():
        element_info = return_elem_row(elem)
        for val in value:
            val = list(val)
            val.extend(element_info)  # append element_info values to val
            material.append(val)
    Graph_node_dataset.append(material)

print(f"Number of graphs: {len(Graph_node_dataset)}")
print(f"Node features dimension: {len(Graph_node_dataset[0][0])}")

# Function to convert adjacency matrix to edge index
def adj_to_edge_index(adj):
    edge_index = []
    for i in range(len(adj)):
        for j in range(len(adj[i])):
            if adj[i, j] == 1.0:
                edge_index.append([i, j])
    return edge_index

# Create edge index dataset
Edge_index_dataset = []
for row in data['Adjacency Matrix']:
    row_list = ast.literal_eval(row)  # Convert string to list
    row_array = np.array(row_list)    # Convert list to np.array
    edge_index = adj_to_edge_index(row_array)
    Edge_index_dataset.append(edge_index)

print(f"Number of edge indices: {len(Edge_index_dataset)}")

# Extract target values (bandgap)
y = data['Bandgap (eV)'].values.tolist()
print(f"Target values (bandgap) - Min: {min(y):.3f}, Max: {max(y):.3f}, Mean: {np.mean(y):.3f}")

# Function to create graph data
def Create_graph_data(node, edge_index, y):
    X = torch.tensor(node)
    edge_index = torch.tensor(edge_index).T
    y = torch.tensor(y)
    return [X, edge_index, y]

# Create graphs
Graphs = []
for i in range(len(y)):
    graph = Create_graph_data(Graph_node_dataset[i], Edge_index_dataset[i], y[i])
    Graphs.append(graph)

print(f"Number of graphs created: {len(Graphs)}")
print(f"First graph - Nodes: {Graphs[0][0].shape}, Edges: {Graphs[0][1].shape}, Target: {Graphs[0][2]}")

# Get the actual feature dimension
actual_feature_dim = Graphs[0][0].shape[1]
print(f"Actual node feature dimension: {actual_feature_dim}")

print("\n=== Defining Optimized GIN Model ===")

# Advanced GIN model with multiple architecture variants
class AdvancedGIN(torch.nn.Module):
    """Advanced Graph Isomorphism Network with multiple architectural improvements"""
    def __init__(self, input_dim, dim_h, num_layers=3, dropout_rate=0.5, pooling_type='mean',
                 architecture='enhanced', use_attention=False, use_skip_connections=True):
        super(AdvancedGIN, self).__init__()
        self.num_layers = num_layers
        self.dropout_rate = dropout_rate
        self.pooling_type = pooling_type
        self.architecture = architecture
        self.use_attention = use_attention
        self.use_skip_connections = use_skip_connections

        # Input feature engineering
        self.input_proj = Linear(input_dim, dim_h)
        self.input_bn = BatchNorm1d(dim_h)
        self.input_dropout = torch.nn.Dropout(dropout_rate * 0.3)

        # Create GIN layers with different architectures
        self.convs = torch.nn.ModuleList()
        self.batch_norms = torch.nn.ModuleList()
        self.layer_norms = torch.nn.ModuleList()

        for i in range(num_layers):
            if architecture == 'enhanced':
                # Enhanced architecture with deeper MLPs
                mlp = Sequential(
                    Linear(dim_h, dim_h * 2),
                    BatchNorm1d(dim_h * 2),
                    ReLU(),
                    torch.nn.Dropout(dropout_rate * 0.3),
                    Linear(dim_h * 2, dim_h * 2),
                    BatchNorm1d(dim_h * 2),
                    ReLU(),
                    torch.nn.Dropout(dropout_rate * 0.3),
                    Linear(dim_h * 2, dim_h)
                )
            elif architecture == 'deep':
                # Deeper MLP architecture
                mlp = Sequential(
                    Linear(dim_h, dim_h),
                    BatchNorm1d(dim_h),
                    ReLU(),
                    Linear(dim_h, dim_h * 2),
                    BatchNorm1d(dim_h * 2),
                    ReLU(),
                    Linear(dim_h * 2, dim_h),
                    BatchNorm1d(dim_h),
                    ReLU(),
                    Linear(dim_h, dim_h)
                )
            else:  # 'standard'
                mlp = Sequential(
                    Linear(dim_h, dim_h),
                    BatchNorm1d(dim_h),
                    ReLU(),
                    Linear(dim_h, dim_h)
                )

            self.convs.append(GINConv(mlp))
            self.batch_norms.append(BatchNorm1d(dim_h))
            self.layer_norms.append(torch.nn.LayerNorm(dim_h))

        # Attention mechanism for layer outputs
        if use_attention:
            self.attention = torch.nn.MultiheadAttention(dim_h, num_heads=4, batch_first=True)
            self.attention_norm = torch.nn.LayerNorm(dim_h)

        # Advanced readout with multiple pooling strategies
        if pooling_type == 'multi':
            # Use multiple pooling strategies
            readout_dim = dim_h * num_layers * 3  # mean, max, add
        else:
            readout_dim = dim_h * num_layers

        # Progressive readout network
        self.readout_layers = torch.nn.ModuleList([
            Linear(readout_dim, readout_dim // 2),
            Linear(readout_dim // 2, readout_dim // 4),
            Linear(readout_dim // 4, readout_dim // 8),
            Linear(readout_dim // 8, 1)
        ])

        self.readout_bns = torch.nn.ModuleList([
            BatchNorm1d(readout_dim // 2),
            BatchNorm1d(readout_dim // 4),
            BatchNorm1d(readout_dim // 8)
        ])

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Advanced weight initialization"""
        for m in self.modules():
            if isinstance(m, Linear):
                if m.out_features == 1:  # Output layer
                    torch.nn.init.xavier_normal_(m.weight, gain=0.1)
                else:
                    torch.nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    torch.nn.init.zeros_(m.bias)

    def forward(self, x, edge_index, batch):
        # Enhanced input processing
        h = self.input_dropout(F.relu(self.input_bn(self.input_proj(x))))

        layer_outputs = []

        for i, (conv, bn, ln) in enumerate(zip(self.convs, self.batch_norms, self.layer_norms)):
            h_new = conv(h, edge_index)
            h_new = bn(h_new)

            # Skip connections for deeper networks
            if self.use_skip_connections and i > 0 and h.shape == h_new.shape:
                h = h + h_new
            else:
                h = h_new

            # Layer normalization
            h = ln(h)
            h = F.dropout(h, p=self.dropout_rate * 0.4, training=self.training)
            layer_outputs.append(h)

        # Attention mechanism on layer outputs
        if self.use_attention and len(layer_outputs) > 1:
            # Stack layer outputs for attention
            stacked = torch.stack(layer_outputs, dim=1)  # [num_nodes, num_layers, dim_h]
            attended, _ = self.attention(stacked, stacked, stacked)
            attended = self.attention_norm(attended + stacked)
            layer_outputs = [attended[:, i, :] for i in range(attended.shape[1])]

        # Advanced pooling strategies
        if self.pooling_type == 'multi':
            pooled_outputs = []
            for h in layer_outputs:
                mean_pool = global_mean_pool(h, batch)
                add_pool = global_add_pool(h, batch)
                max_pool = global_mean_pool(torch.relu(h), batch)  # Approximation of max pooling
                pooled_outputs.extend([mean_pool, add_pool, max_pool])
        elif self.pooling_type == 'mean':
            pooled_outputs = [global_mean_pool(h, batch) for h in layer_outputs]
        else:  # add
            pooled_outputs = [global_add_pool(h, batch) for h in layer_outputs]

        graph_repr = torch.cat(pooled_outputs, dim=1)

        # Progressive readout with residual connections
        h = graph_repr
        for i, (linear, bn) in enumerate(zip(self.readout_layers[:-1], self.readout_bns)):
            h_new = F.relu(bn(linear(h)))
            h_new = F.dropout(h_new, p=self.dropout_rate * (0.6 - i * 0.1), training=self.training)
            h = h_new

        # Final output
        output = self.readout_layers[-1](h)
        return output.squeeze()

# Keep the original GIN for compatibility
GIN = AdvancedGIN

# Configurable training function
def train_epoch(model, loader, optimizer, criterion):
    model.train()
    total_loss = 0
    for data in loader:
        optimizer.zero_grad()
        out = model(data.x, data.edge_index, data.batch)
        loss = criterion(out, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    return total_loss / len(loader)

# Test function
def test(model, loader):
    model.eval()
    total_loss = 0
    predictions = []
    targets = []
    criterion = torch.nn.MSELoss()

    with torch.no_grad():
        for data in loader:
            out = model(data.x, data.edge_index, data.batch)
            loss = criterion(out, data.y)
            total_loss += loss.item()
            predictions.extend(out.cpu().numpy())
            targets.extend(data.y.cpu().numpy())

    return total_loss / len(loader), np.array(predictions), np.array(targets)

print("GIN model and training functions defined!")

# Enhanced optimization function with advanced architectures
def train_and_evaluate_model(config):
    """Train and evaluate a model with given configuration"""
    arch_str = f"Arch: {config.get('architecture', 'enhanced')}"
    if config.get('use_attention', False):
        arch_str += "+Attention"
    if config.get('use_skip_connections', True):
        arch_str += "+Skip"

    print(f"\n--- Testing Configuration ---")
    print(f"Layers: {config['num_layers']}, Hidden: {config['dim_h']}, "
          f"Dropout: {config['dropout']}, LR: {config['lr']}, "
          f"Batch: {config['batch_size']}, Pooling: {config['pooling']}, {arch_str}")

    # Create data loaders with specified batch size
    train_loader = DataLoader(train_graphs, batch_size=config['batch_size'], shuffle=True)
    test_loader = DataLoader(test_graphs, batch_size=config['batch_size'], shuffle=False)

    # Initialize model with advanced architecture
    model = AdvancedGIN(
        input_dim=actual_feature_dim,
        dim_h=config['dim_h'],
        num_layers=config['num_layers'],
        dropout_rate=config['dropout'],
        pooling_type=config['pooling'],
        architecture=config.get('architecture', 'enhanced'),
        use_attention=config.get('use_attention', False),
        use_skip_connections=config.get('use_skip_connections', True)
    )

    # Setup training with advanced optimizers
    criterion = torch.nn.MSELoss()

    if config.get('optimizer', 'adam') == 'adamw':
        optimizer = torch.optim.AdamW(model.parameters(), lr=config['lr'], weight_decay=config.get('weight_decay', 1e-5))
    elif config.get('optimizer', 'adam') == 'sgd':
        optimizer = torch.optim.SGD(model.parameters(), lr=config['lr'], momentum=0.9, weight_decay=config.get('weight_decay', 1e-4))
    else:
        optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'], weight_decay=config.get('weight_decay', 1e-6))

    # Learning rate scheduler
    if config.get('use_scheduler', False):
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.7, patience=10, verbose=False)

    # Training loop with advanced techniques
    train_losses = []
    test_losses = []
    best_test_loss = float('inf')
    patience_counter = 0
    patience = config.get('patience', 25)

    for epoch in range(config['epochs']):
        train_loss = train_epoch(model, train_loader, optimizer, criterion)
        test_loss, _, _ = test(model, test_loader)

        train_losses.append(train_loss)
        test_losses.append(test_loss)

        # Learning rate scheduling
        if config.get('use_scheduler', False):
            scheduler.step(test_loss)

        # Early stopping with improved patience
        if test_loss < best_test_loss:
            best_test_loss = test_loss
            patience_counter = 0
        else:
            patience_counter += 1

        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch}")
            break

        if epoch % 20 == 0:
            print(f'Epoch {epoch:03d}, Train: {train_loss:.4f}, Test: {test_loss:.4f}')

    # Final evaluation
    final_test_loss, predictions, targets = test(model, test_loader)
    mse = mean_squared_error(targets, predictions)
    mae = mean_absolute_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    rmse = np.sqrt(mse)

    return {
        'config': config,
        'model': model,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'test_loss': final_test_loss,
        'train_losses': train_losses,
        'test_losses': test_losses,
        'predictions': predictions,
        'targets': targets,
        'parameters': sum(p.numel() for p in model.parameters())
    }

print("\n=== Preparing Data ===")

# Feature normalization for better training stability
print("Applying feature normalization...")

# Collect all node features for normalization
all_features = []
for graph in Graphs:
    X, _, _ = graph
    all_features.append(X.numpy())

all_features = np.vstack(all_features)
feature_mean = np.mean(all_features, axis=0)
feature_std = np.std(all_features, axis=0) + 1e-8  # Add small epsilon to avoid division by zero

print(f"Feature statistics - Mean: {feature_mean[:3]}, Std: {feature_std[:3]}")

# Prepare PyG Data objects with normalized features
pyg_graphs = []
for graph in Graphs:
    X, edge_index, y_val = graph
    # Normalize features
    X_normalized = (X.numpy() - feature_mean) / feature_std
    data = Data(x=torch.tensor(X_normalized).float(), edge_index=edge_index.long(), y=y_val.view(1).float())
    pyg_graphs.append(data)

print(f"Created {len(pyg_graphs)} PyG graph objects with normalized features")

# Split dataset into train and test
split_idx = int(0.8 * len(pyg_graphs))
train_graphs = pyg_graphs[:split_idx]
test_graphs = pyg_graphs[split_idx:]

print(f"Training graphs: {len(train_graphs)}")
print(f"Test graphs: {len(test_graphs)}")

print("\n=== SYSTEMATIC MODEL OPTIMIZATION ===")

# Ultra-advanced optimization configurations
print("� ULTRA-ADVANCED OPTIMIZATION PHASE")
print("Testing cutting-edge architectures and techniques...")

configs = [
    # Best baseline with enhanced architecture
    {'num_layers': 2, 'dim_h': 64, 'dropout': 0.35, 'lr': 0.0025, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'enhanced', 'use_attention': False, 'use_skip_connections': True},

    # Enhanced architecture variants
    {'num_layers': 2, 'dim_h': 80, 'dropout': 0.3, 'lr': 0.002, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'enhanced', 'use_attention': False, 'use_skip_connections': True},

    {'num_layers': 3, 'dim_h': 56, 'dropout': 0.35, 'lr': 0.0015, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'enhanced', 'use_attention': False, 'use_skip_connections': True},

    # Deep architecture experiments
    {'num_layers': 2, 'dim_h': 64, 'dropout': 0.4, 'lr': 0.002, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'deep', 'use_attention': False, 'use_skip_connections': True},

    {'num_layers': 3, 'dim_h': 48, 'dropout': 0.45, 'lr': 0.0015, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'deep', 'use_attention': False, 'use_skip_connections': True},

    # Attention mechanism experiments
    {'num_layers': 2, 'dim_h': 64, 'dropout': 0.35, 'lr': 0.002, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'enhanced', 'use_attention': True, 'use_skip_connections': True},

    {'num_layers': 3, 'dim_h': 56, 'dropout': 0.4, 'lr': 0.0015, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'enhanced', 'use_attention': True, 'use_skip_connections': True},

    # Multi-pooling experiments
    {'num_layers': 2, 'dim_h': 48, 'dropout': 0.35, 'lr': 0.002, 'batch_size': 32, 'pooling': 'multi',
     'epochs': 200, 'architecture': 'enhanced', 'use_attention': False, 'use_skip_connections': True},

    {'num_layers': 3, 'dim_h': 40, 'dropout': 0.4, 'lr': 0.0015, 'batch_size': 32, 'pooling': 'multi',
     'epochs': 200, 'architecture': 'enhanced', 'use_attention': False, 'use_skip_connections': True},

    # Advanced optimizer experiments
    {'num_layers': 2, 'dim_h': 64, 'dropout': 0.35, 'lr': 0.003, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'enhanced', 'optimizer': 'adamw', 'weight_decay': 1e-4, 'use_scheduler': True},

    {'num_layers': 2, 'dim_h': 80, 'dropout': 0.3, 'lr': 0.002, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'enhanced', 'optimizer': 'adamw', 'weight_decay': 5e-5, 'use_scheduler': True},

    # Ultra-fine-tuned configurations
    {'num_layers': 2, 'dim_h': 72, 'dropout': 0.32, 'lr': 0.0022, 'batch_size': 28, 'pooling': 'mean',
     'epochs': 250, 'architecture': 'enhanced', 'use_attention': False, 'use_skip_connections': True, 'patience': 30},

    {'num_layers': 2, 'dim_h': 68, 'dropout': 0.33, 'lr': 0.0018, 'batch_size': 30, 'pooling': 'mean',
     'epochs': 250, 'architecture': 'enhanced', 'optimizer': 'adamw', 'weight_decay': 2e-5, 'use_scheduler': True, 'patience': 35},

    # Ensemble-ready configurations
    {'num_layers': 3, 'dim_h': 60, 'dropout': 0.38, 'lr': 0.0012, 'batch_size': 32, 'pooling': 'mean',
     'epochs': 200, 'architecture': 'deep', 'use_attention': True, 'use_skip_connections': True},

    {'num_layers': 2, 'dim_h': 76, 'dropout': 0.28, 'lr': 0.0028, 'batch_size': 32, 'pooling': 'multi',
     'epochs': 200, 'architecture': 'enhanced', 'use_attention': False, 'use_skip_connections': True},
]

# Run optimization experiments
results = []
best_result = None
best_rmse = float('inf')

print(f"Running {len(configs)} optimization experiments...")

for i, config in enumerate(configs):
    print(f"\n{'='*60}")
    print(f"EXPERIMENT {i+1}/{len(configs)}")
    print(f"{'='*60}")

    try:
        result = train_and_evaluate_model(config)
        results.append(result)

        print(f"Results: RMSE={result['rmse']:.4f}, MAE={result['mae']:.4f}, R²={result['r2']:.4f}")
        print(f"Parameters: {result['parameters']}")

        if result['rmse'] < best_rmse:
            best_rmse = result['rmse']
            best_result = result
            print(f"🎉 NEW BEST MODEL! RMSE: {best_rmse:.4f}")

    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        continue

print("\n" + "="*60)
print("OPTIMIZATION RESULTS SUMMARY")
print("="*60)

if best_result:
    print(f"\n🏆 BEST MODEL CONFIGURATION:")
    config = best_result['config']
    print(f"   • Layers: {config['num_layers']}")
    print(f"   • Hidden dimension: {config['dim_h']}")
    print(f"   • Dropout rate: {config['dropout']}")
    print(f"   • Learning rate: {config['lr']}")
    print(f"   • Batch size: {config['batch_size']}")
    print(f"   • Pooling: {config['pooling']}")
    print(f"   • Parameters: {best_result['parameters']:,}")

    print(f"\n🎯 BEST PERFORMANCE METRICS:")
    print(f"   • Test RMSE: {best_result['rmse']:.4f} eV")
    print(f"   • Test MAE: {best_result['mae']:.4f} eV")
    print(f"   • Test R²: {best_result['r2']:.4f}")
    print(f"   • Test Loss: {best_result['test_loss']:.4f}")

    # Check if we met success criteria
    success_rmse = best_result['rmse'] < 0.5
    success_r2 = best_result['r2'] > 0

    print(f"\n✅ SUCCESS CRITERIA:")
    print(f"   • RMSE < 0.5 eV: {'✓' if success_rmse else '✗'} ({best_result['rmse']:.4f})")
    print(f"   • R² > 0: {'✓' if success_r2 else '✗'} ({best_result['r2']:.4f})")

    if success_rmse and success_r2:
        print(f"\n🎉 ALL SUCCESS CRITERIA MET!")
    else:
        print(f"\n⚠️  Some criteria not met - consider further optimization")

# Show top 5 results
print(f"\n📊 TOP 5 CONFIGURATIONS BY RMSE:")
sorted_results = sorted(results, key=lambda x: x['rmse'])
for i, result in enumerate(sorted_results[:5]):
    config = result['config']
    print(f"{i+1}. RMSE: {result['rmse']:.4f}, R²: {result['r2']:.4f} "
          f"[L:{config['num_layers']}, H:{config['dim_h']}, "
          f"D:{config['dropout']}, LR:{config['lr']}, B:{config['batch_size']}]")

print(f"\n=== FINAL OPTIMIZED MODEL TRAINING ===")
if best_result:
    print("Training final model with best configuration...")
    final_result = train_and_evaluate_model(best_result['config'])

    print(f"\n=== FINAL MODEL EVALUATION ===")
    print(f"Final RMSE: {final_result['rmse']:.4f} eV")
    print(f"Final MAE: {final_result['mae']:.4f} eV")
    print(f"Final R²: {final_result['r2']:.4f}")
    print(f"Model parameters: {final_result['parameters']:,}")

print(f"\n=== DATASET SUMMARY ===")
print(f"Total materials: {len(pyg_graphs)}")
print(f"Node features per atom: {actual_feature_dim}")
print(f"Average nodes per graph: {np.mean([g.x.shape[0] for g in pyg_graphs]):.2f}")
print(f"Average edges per graph: {np.mean([g.edge_index.shape[1] for g in pyg_graphs]):.2f}")
print(f"Bandgap range: {min(y):.3f} - {max(y):.3f} eV")

print(f"\n🚀 GIN MODEL OPTIMIZATION COMPLETED!")
print(f"Tested {len(configs)} different configurations")
print(f"Best RMSE achieved: {best_rmse:.4f} eV")
print("="*60)

# Create comprehensive visualizations
print("\n🎨 CREATING PERFORMANCE VISUALIZATIONS...")

import matplotlib.pyplot as plt
import seaborn as sns
plt.style.use('default')
sns.set_palette("husl")

def create_comprehensive_visualizations(results, best_result):
    """Create comprehensive performance visualizations"""

    # Set up the figure with subplots
    fig = plt.figure(figsize=(24, 18))

    # 1. Training curves for best model
    plt.subplot(4, 5, 1)
    if best_result and 'train_losses' in best_result:
        epochs = range(len(best_result['train_losses']))
        plt.plot(epochs, best_result['train_losses'], label='Train Loss', color='blue', linewidth=2)
        plt.plot(epochs, best_result['test_losses'], label='Test Loss', color='red', linewidth=2)
        plt.xlabel('Epoch')
        plt.ylabel('MSE Loss')
        plt.title('Best Model Training Curves')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 2. Training curves (log scale)
    plt.subplot(4, 5, 2)
    if best_result and 'train_losses' in best_result:
        plt.plot(epochs, best_result['train_losses'], label='Train Loss', color='blue', linewidth=2)
        plt.plot(epochs, best_result['test_losses'], label='Test Loss', color='red', linewidth=2)
        plt.xlabel('Epoch')
        plt.ylabel('MSE Loss (log)')
        plt.title('Training Curves (Log Scale)')
        plt.yscale('log')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 3. Predictions vs Actual for best model
    plt.subplot(4, 5, 3)
    if best_result and 'predictions' in best_result:
        predictions = best_result['predictions']
        targets = best_result['targets']
        plt.scatter(targets, predictions, alpha=0.7, color='blue', s=50, edgecolors='black', linewidth=0.5)
        min_val, max_val = min(targets.min(), predictions.min()), max(targets.max(), predictions.max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')
        plt.xlabel('Actual Bandgap (eV)')
        plt.ylabel('Predicted Bandgap (eV)')
        plt.title(f'Best Model: Predictions vs Actual\nR² = {best_result["r2"]:.4f}')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 4. Residuals plot
    plt.subplot(4, 5, 4)
    if best_result and 'predictions' in best_result:
        residuals = predictions - targets
        plt.scatter(targets, residuals, alpha=0.7, color='green', s=50, edgecolors='black', linewidth=0.5)
        plt.axhline(y=0, color='r', linestyle='--', linewidth=2)
        plt.xlabel('Actual Bandgap (eV)')
        plt.ylabel('Residuals (eV)')
        plt.title('Residuals Plot')
        plt.grid(True, alpha=0.3)

    # 5. Error distribution
    plt.subplot(4, 5, 5)
    if best_result and 'predictions' in best_result:
        plt.hist(residuals, bins=15, alpha=0.7, color='orange', edgecolor='black', density=True)
        plt.xlabel('Residuals (eV)')
        plt.ylabel('Density')
        plt.title('Error Distribution')
        plt.grid(True, alpha=0.3)

        # Add normal distribution overlay
        mu, sigma = np.mean(residuals), np.std(residuals)
        x = np.linspace(residuals.min(), residuals.max(), 100)
        plt.plot(x, (1/(sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu) / sigma) ** 2),
                'r-', linewidth=2, label=f'Normal(μ={mu:.3f}, σ={sigma:.3f})')
        plt.legend()

    # 6. Model performance comparison
    plt.subplot(4, 5, 6)
    if results:
        rmse_values = [r['rmse'] for r in results]
        r2_values = [r['r2'] for r in results]
        colors = ['red' if r['rmse'] == min(rmse_values) else 'blue' for r in results]
        plt.scatter(rmse_values, r2_values, c=colors, alpha=0.7, s=60, edgecolors='black', linewidth=0.5)
        plt.xlabel('RMSE (eV)')
        plt.ylabel('R² Score')
        plt.title('Model Performance Comparison')
        plt.grid(True, alpha=0.3)

        # Highlight best model
        best_idx = rmse_values.index(min(rmse_values))
        plt.scatter(rmse_values[best_idx], r2_values[best_idx], c='red', s=200, marker='*',
                   edgecolors='black', linewidth=2, label='Best Model')
        plt.legend()

    # 7. Architecture performance analysis
    plt.subplot(4, 5, 7)
    if results:
        arch_performance = {}
        for r in results:
            arch = r['config'].get('architecture', 'enhanced')
            if arch not in arch_performance:
                arch_performance[arch] = []
            arch_performance[arch].append(r['rmse'])

        archs = list(arch_performance.keys())
        avg_rmse = [np.mean(arch_performance[arch]) for arch in archs]
        colors = plt.cm.Set3(np.linspace(0, 1, len(archs)))

        bars = plt.bar(archs, avg_rmse, color=colors, alpha=0.7, edgecolor='black', linewidth=1)
        plt.xlabel('Architecture Type')
        plt.ylabel('Average RMSE (eV)')
        plt.title('Architecture Performance')
        plt.xticks(rotation=45)

        # Add value labels on bars
        for bar, val in zip(bars, avg_rmse):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{val:.3f}', ha='center', va='bottom', fontweight='bold')

    # 8. Learning rate vs performance
    plt.subplot(4, 5, 8)
    if results:
        lr_values = [r['config']['lr'] for r in results]
        rmse_values = [r['rmse'] for r in results]
        plt.scatter(lr_values, rmse_values, alpha=0.7, color='purple', s=60, edgecolors='black', linewidth=0.5)
        plt.xlabel('Learning Rate')
        plt.ylabel('RMSE (eV)')
        plt.title('Learning Rate vs Performance')
        plt.xscale('log')
        plt.grid(True, alpha=0.3)

    # 9. Model complexity vs performance
    plt.subplot(4, 5, 9)
    if results:
        param_counts = [r['parameters'] for r in results]
        rmse_values = [r['rmse'] for r in results]
        plt.scatter(param_counts, rmse_values, alpha=0.7, color='brown', s=60, edgecolors='black', linewidth=0.5)
        plt.xlabel('Number of Parameters')
        plt.ylabel('RMSE (eV)')
        plt.title('Model Complexity vs Performance')
        plt.grid(True, alpha=0.3)

    # 10. Dropout rate analysis
    plt.subplot(4, 5, 10)
    if results:
        dropout_values = [r['config']['dropout'] for r in results]
        rmse_values = [r['rmse'] for r in results]
        plt.scatter(dropout_values, rmse_values, alpha=0.7, color='teal', s=60, edgecolors='black', linewidth=0.5)
        plt.xlabel('Dropout Rate')
        plt.ylabel('RMSE (eV)')
        plt.title('Dropout Rate vs Performance')
        plt.grid(True, alpha=0.3)

    # 11. Hidden dimension analysis
    plt.subplot(4, 5, 11)
    if results:
        hidden_dims = [r['config']['dim_h'] for r in results]
        rmse_values = [r['rmse'] for r in results]
        plt.scatter(hidden_dims, rmse_values, alpha=0.7, color='olive', s=60, edgecolors='black', linewidth=0.5)
        plt.xlabel('Hidden Dimension')
        plt.ylabel('RMSE (eV)')
        plt.title('Hidden Dimension vs Performance')
        plt.grid(True, alpha=0.3)

    # 12. Number of layers analysis
    plt.subplot(4, 5, 12)
    if results:
        layer_counts = [r['config']['num_layers'] for r in results]
        rmse_values = [r['rmse'] for r in results]
        plt.scatter(layer_counts, rmse_values, alpha=0.7, color='navy', s=60, edgecolors='black', linewidth=0.5)
        plt.xlabel('Number of Layers')
        plt.ylabel('RMSE (eV)')
        plt.title('Network Depth vs Performance')
        plt.grid(True, alpha=0.3)

    # 13. Performance metrics comparison
    plt.subplot(4, 5, 13)
    if best_result:
        metrics = ['RMSE', 'MAE', 'R²']
        values = [best_result['rmse'], best_result['mae'], best_result['r2']]
        colors = ['red', 'orange', 'green']
        bars = plt.bar(metrics, values, color=colors, alpha=0.7, edgecolor='black', linewidth=1)
        plt.title('Best Model Performance Metrics')
        plt.ylabel('Value')

        # Add value labels
        for bar, value in zip(bars, values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.4f}', ha='center', va='bottom', fontweight='bold')

    # 14. Top 5 models comparison
    plt.subplot(4, 5, 14)
    if results:
        sorted_results = sorted(results, key=lambda x: x['rmse'])[:5]
        model_names = [f"Model {i+1}" for i in range(len(sorted_results))]
        rmse_vals = [r['rmse'] for r in sorted_results]

        bars = plt.bar(model_names, rmse_vals, color='skyblue', alpha=0.7, edgecolor='black', linewidth=1)
        plt.xlabel('Top Models')
        plt.ylabel('RMSE (eV)')
        plt.title('Top 5 Models Performance')
        plt.xticks(rotation=45)

        # Add value labels
        for bar, val in zip(bars, rmse_vals):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                    f'{val:.3f}', ha='center', va='bottom', fontweight='bold')

    # 15. Bandgap distribution with predictions
    plt.subplot(4, 5, 15)
    if best_result and 'predictions' in best_result:
        plt.hist(targets, bins=20, alpha=0.5, label='Actual', color='blue', edgecolor='black')
        plt.hist(predictions, bins=20, alpha=0.5, label='Predicted', color='red', edgecolor='black')
        plt.xlabel('Bandgap (eV)')
        plt.ylabel('Frequency')
        plt.title('Bandgap Distribution: Actual vs Predicted')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 16. Model summary text
    plt.subplot(4, 5, 16)
    plt.axis('off')
    if best_result:
        config = best_result['config']
        summary_text = f"""
BEST MODEL SUMMARY

Architecture: {config.get('architecture', 'enhanced')}
Layers: {config['num_layers']}
Hidden Dim: {config['dim_h']}
Dropout: {config['dropout']}
Learning Rate: {config['lr']}
Batch Size: {config['batch_size']}
Pooling: {config['pooling']}

Performance:
RMSE: {best_result['rmse']:.4f} eV
MAE: {best_result['mae']:.4f} eV
R²: {best_result['r2']:.4f}

Parameters: {best_result['parameters']:,}

Features:
• Attention: {config.get('use_attention', False)}
• Skip Connections: {config.get('use_skip_connections', True)}
• Optimizer: {config.get('optimizer', 'adam')}
• Scheduler: {config.get('use_scheduler', False)}
        """
        plt.text(0.1, 0.9, summary_text, transform=plt.gca().transAxes,
                fontsize=9, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))

    # 17. Optimization progress
    plt.subplot(4, 5, 17)
    if results:
        experiment_nums = list(range(1, len(results) + 1))
        rmse_progression = [r['rmse'] for r in results]
        best_so_far = []
        current_best = float('inf')

        for rmse in rmse_progression:
            if rmse < current_best:
                current_best = rmse
            best_so_far.append(current_best)

        plt.plot(experiment_nums, rmse_progression, 'o-', alpha=0.7, label='Experiment RMSE')
        plt.plot(experiment_nums, best_so_far, 'r-', linewidth=3, label='Best So Far')
        plt.xlabel('Experiment Number')
        plt.ylabel('RMSE (eV)')
        plt.title('Optimization Progress')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 18. Feature importance (if available)
    plt.subplot(4, 5, 18)
    plt.text(0.5, 0.5, 'Feature Analysis\n(Graph-based features)',
             ha='center', va='center', transform=plt.gca().transAxes,
             fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
    plt.axis('off')

    # 19. Convergence analysis
    plt.subplot(4, 5, 19)
    if best_result and 'train_losses' in best_result:
        train_losses = best_result['train_losses']
        test_losses = best_result['test_losses']

        # Calculate convergence metrics
        train_convergence = np.diff(train_losses)
        test_convergence = np.diff(test_losses)

        plt.plot(train_convergence, label='Train Loss Change', alpha=0.7)
        plt.plot(test_convergence, label='Test Loss Change', alpha=0.7)
        plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        plt.xlabel('Epoch')
        plt.ylabel('Loss Change')
        plt.title('Convergence Analysis')
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 20. Final performance summary
    plt.subplot(4, 5, 20)
    plt.axis('off')
    if best_result:
        improvement_text = f"""
OPTIMIZATION RESULTS

Initial RMSE: 0.7571 eV
Final RMSE: {best_result['rmse']:.4f} eV
Improvement: {((0.7571 - best_result['rmse'])/0.7571)*100:.1f}%

Initial R²: -0.2338
Final R²: {best_result['r2']:.4f}
Improvement: {best_result['r2'] - (-0.2338):.4f}

Success Criteria:
✓ RMSE < 0.5 eV: {'✓' if best_result['rmse'] < 0.5 else '✗'}
✓ R² > 0: {'✓' if best_result['r2'] > 0 else '✗'}

Total Experiments: {len(results)}
Best Architecture: {best_result['config'].get('architecture', 'enhanced')}
        """
        plt.text(0.1, 0.9, improvement_text, transform=plt.gca().transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))

    plt.tight_layout()
    plt.savefig('advanced_gin_optimization_results.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ Comprehensive visualizations created and saved as 'advanced_gin_optimization_results.png'")

# Create the visualizations
if results:
    create_comprehensive_visualizations(results, best_result)
else:
    print("⚠️ No results available for visualization")

print("\n🎊 ADVANCED GIN MODEL OPTIMIZATION WITH VISUALIZATIONS COMPLETED!")
print("="*80)
