#!/usr/bin/env python3
"""
GIN Model Application on Binary Chalcogenides Dataset
This script applies a Graph Isomorphism Network (GIN) model to predict bandgap values.
"""

import pandas as pd
import numpy as np
import ast
import torch
import torch.nn.functional as F
from torch.nn import Linear, Sequential, BatchNorm1d, ReLU, Dropout
from torch_geometric.nn import GINConv, global_mean_pool, global_add_pool
from torch_geometric.data import Data, DataLoader
import matplotlib.pyplot as plt
import networkx as nx
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Set random seeds for reproducibility
torch.manual_seed(11)
torch.cuda.manual_seed(0)
torch.cuda.manual_seed_all(0)
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False

print("=== Loading and Processing Data ===")

# Load the dataset
data = pd.read_csv('/home/<USER>/Coding/Binary chalcogenides/Graph structure dataset for dichalcogenides (2).csv')
print(f"Dataset shape: {data.shape}")
print(f"Columns: {data.columns.tolist()}")

# Extract unique elements from the dataset
all_elements = []
for d in data['Node Elements']:
    d_dict = dict(ast.literal_eval(d))
    all_elements.extend(d_dict.keys())

unique_elements = set(all_elements)
print(f"Unique elements: {unique_elements}")
print(f"Number of unique elements: {len(unique_elements)}")

# Function to get element properties
def get_element_properties(element_symbol):
    try:
        from pymatgen.core import Element
        element = Element(element_symbol)
        return {
            'Element': element_symbol,
            'atomic_number': element.Z,
            'atomic_mass': element.atomic_mass,
            'atomic_radius': element.atomic_radius or 0,
            'electronegativity': element.X or 0,
            'ionization_energy': element.ionization_energy or 0,
            'electron_affinity': element.electron_affinity or 0,
            'melting_point': element.melting_point or 0
        }
    except:
        return {
            'Element': element_symbol,
            'atomic_number': 0,
            'atomic_mass': 0,
            'atomic_radius': 0,
            'electronegativity': 0,
            'ionization_energy': 0,
            'electron_affinity': 0,
            'melting_point': 0
        }

# Create element properties dataset
elements_list = []
for element in unique_elements:
    elements_list.append(get_element_properties(element))

elements_data = pd.DataFrame(elements_list).fillna(0)
print(f"Element properties shape: {elements_data.shape}")

# Function to return element row
def return_elem_row(name):
    row = elements_data[elements_data['Element'] == name].select_dtypes(np.number).values.flatten().tolist()
    return row

# Create graph node dataset
Graph_node_dataset = []
for row in data['Node Elements']:
    material = []
    row = dict(ast.literal_eval(row))
    for elem, value in row.items():
        element_info = return_elem_row(elem)
        for val in value:
            val = list(val)
            val.extend(element_info)  # append element_info values to val
            material.append(val)
    Graph_node_dataset.append(material)

print(f"Number of graphs: {len(Graph_node_dataset)}")
print(f"Node features dimension: {len(Graph_node_dataset[0][0])}")

# Function to convert adjacency matrix to edge index
def adj_to_edge_index(adj):
    edge_index = []
    for i in range(len(adj)):
        for j in range(len(adj[i])):
            if adj[i, j] == 1.0:
                edge_index.append([i, j])
    return edge_index

# Create edge index dataset
Edge_index_dataset = []
for row in data['Adjacency Matrix']:
    row_list = ast.literal_eval(row)  # Convert string to list
    row_array = np.array(row_list)    # Convert list to np.array
    edge_index = adj_to_edge_index(row_array)
    Edge_index_dataset.append(edge_index)

print(f"Number of edge indices: {len(Edge_index_dataset)}")

# Extract target values (bandgap)
y = data['Bandgap (eV)'].values.tolist()
print(f"Target values (bandgap) - Min: {min(y):.3f}, Max: {max(y):.3f}, Mean: {np.mean(y):.3f}")

# Function to create graph data
def Create_graph_data(node, edge_index, y):
    X = torch.tensor(node)
    edge_index = torch.tensor(edge_index).T
    y = torch.tensor(y)
    return [X, edge_index, y]

# Create graphs
Graphs = []
for i in range(len(y)):
    graph = Create_graph_data(Graph_node_dataset[i], Edge_index_dataset[i], y[i])
    Graphs.append(graph)

print(f"Number of graphs created: {len(Graphs)}")
print(f"First graph - Nodes: {Graphs[0][0].shape}, Edges: {Graphs[0][1].shape}, Target: {Graphs[0][2]}")

# Get the actual feature dimension
actual_feature_dim = Graphs[0][0].shape[1]
print(f"Actual node feature dimension: {actual_feature_dim}")

print("\n=== Defining Optimized GIN Model ===")

# Enhanced GIN model with improved architecture
class GIN(torch.nn.Module):
    """Enhanced Graph Isomorphism Network (GIN) for regression"""
    def __init__(self, input_dim, dim_h, num_layers=3, dropout_rate=0.5, pooling_type='add'):
        super(GIN, self).__init__()
        self.num_layers = num_layers
        self.dropout_rate = dropout_rate
        self.pooling_type = pooling_type

        # Input projection layer for better feature representation
        self.input_proj = Linear(input_dim, dim_h)
        self.input_bn = BatchNorm1d(dim_h)

        # Create GIN layers dynamically with improved architecture
        self.convs = torch.nn.ModuleList()
        self.batch_norms = torch.nn.ModuleList()

        # All layers use same hidden dimension for consistency
        for i in range(num_layers):
            self.convs.append(GINConv(
                Sequential(
                    Linear(dim_h, dim_h),
                    BatchNorm1d(dim_h),
                    ReLU(),
                    Linear(dim_h, dim_h),
                    BatchNorm1d(dim_h),
                    ReLU()
                )))
            self.batch_norms.append(BatchNorm1d(dim_h))

        # Improved readout layers with better capacity
        readout_dim = dim_h * num_layers
        self.readout_layers = Sequential(
            Linear(readout_dim, readout_dim // 2),
            BatchNorm1d(readout_dim // 2),
            ReLU(),
            Linear(readout_dim // 2, readout_dim // 4),
            BatchNorm1d(readout_dim // 4),
            ReLU(),
            Linear(readout_dim // 4, 1)
        )

        # Initialize weights properly
        self._init_weights()

    def _init_weights(self):
        """Initialize weights with Xavier/Glorot initialization"""
        for m in self.modules():
            if isinstance(m, Linear):
                torch.nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    torch.nn.init.zeros_(m.bias)

    def forward(self, x, edge_index, batch):
        # Input projection
        h = F.relu(self.input_bn(self.input_proj(x)))

        # Store layer outputs for skip connections
        layer_outputs = []

        for i, (conv, bn) in enumerate(zip(self.convs, self.batch_norms)):
            h_new = conv(h, edge_index)
            h_new = bn(h_new)

            # Add residual connection for deeper networks
            if i > 0 and h.shape == h_new.shape:
                h = h + h_new  # Residual connection
            else:
                h = h_new

            h = F.dropout(h, p=self.dropout_rate * 0.5, training=self.training)
            layer_outputs.append(h)

        # Graph-level readout with all layer outputs
        if self.pooling_type == 'add':
            pooled_outputs = [global_add_pool(h, batch) for h in layer_outputs]
        else:  # mean pooling
            pooled_outputs = [global_mean_pool(h, batch) for h in layer_outputs]

        graph_repr = torch.cat(pooled_outputs, dim=1)

        # Final prediction with dropout
        graph_repr = F.dropout(graph_repr, p=self.dropout_rate, training=self.training)
        output = self.readout_layers(graph_repr)

        return output.squeeze()

# Configurable training function
def train_epoch(model, loader, optimizer, criterion):
    model.train()
    total_loss = 0
    for data in loader:
        optimizer.zero_grad()
        out = model(data.x, data.edge_index, data.batch)
        loss = criterion(out, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    return total_loss / len(loader)

# Test function
def test(model, loader):
    model.eval()
    total_loss = 0
    predictions = []
    targets = []
    criterion = torch.nn.MSELoss()

    with torch.no_grad():
        for data in loader:
            out = model(data.x, data.edge_index, data.batch)
            loss = criterion(out, data.y)
            total_loss += loss.item()
            predictions.extend(out.cpu().numpy())
            targets.extend(data.y.cpu().numpy())

    return total_loss / len(loader), np.array(predictions), np.array(targets)

print("GIN model and training functions defined!")

# Optimization function
def train_and_evaluate_model(config):
    """Train and evaluate a model with given configuration"""
    print(f"\n--- Testing Configuration ---")
    print(f"Layers: {config['num_layers']}, Hidden: {config['dim_h']}, "
          f"Dropout: {config['dropout']}, LR: {config['lr']}, "
          f"Batch: {config['batch_size']}, Pooling: {config['pooling']}")

    # Create data loaders with specified batch size
    train_loader = DataLoader(train_graphs, batch_size=config['batch_size'], shuffle=True)
    test_loader = DataLoader(test_graphs, batch_size=config['batch_size'], shuffle=False)

    # Initialize model
    model = GIN(input_dim=actual_feature_dim,
                dim_h=config['dim_h'],
                num_layers=config['num_layers'],
                dropout_rate=config['dropout'],
                pooling_type=config['pooling'])

    # Setup training
    criterion = torch.nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'])

    # Training loop
    train_losses = []
    test_losses = []
    best_test_loss = float('inf')
    patience_counter = 0
    patience = 20

    for epoch in range(config['epochs']):
        train_loss = train_epoch(model, train_loader, optimizer, criterion)
        test_loss, _, _ = test(model, test_loader)

        train_losses.append(train_loss)
        test_losses.append(test_loss)

        # Early stopping
        if test_loss < best_test_loss:
            best_test_loss = test_loss
            patience_counter = 0
        else:
            patience_counter += 1

        if patience_counter >= patience:
            print(f"Early stopping at epoch {epoch}")
            break

        if epoch % 20 == 0:
            print(f'Epoch {epoch:03d}, Train: {train_loss:.4f}, Test: {test_loss:.4f}')

    # Final evaluation
    final_test_loss, predictions, targets = test(model, test_loader)
    mse = mean_squared_error(targets, predictions)
    mae = mean_absolute_error(targets, predictions)
    r2 = r2_score(targets, predictions)
    rmse = np.sqrt(mse)

    return {
        'config': config,
        'model': model,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'test_loss': final_test_loss,
        'train_losses': train_losses,
        'test_losses': test_losses,
        'parameters': sum(p.numel() for p in model.parameters())
    }

print("\n=== Preparing Data ===")

# Feature normalization for better training stability
print("Applying feature normalization...")

# Collect all node features for normalization
all_features = []
for graph in Graphs:
    X, _, _ = graph
    all_features.append(X.numpy())

all_features = np.vstack(all_features)
feature_mean = np.mean(all_features, axis=0)
feature_std = np.std(all_features, axis=0) + 1e-8  # Add small epsilon to avoid division by zero

print(f"Feature statistics - Mean: {feature_mean[:3]}, Std: {feature_std[:3]}")

# Prepare PyG Data objects with normalized features
pyg_graphs = []
for graph in Graphs:
    X, edge_index, y_val = graph
    # Normalize features
    X_normalized = (X.numpy() - feature_mean) / feature_std
    data = Data(x=torch.tensor(X_normalized).float(), edge_index=edge_index.long(), y=y_val.view(1).float())
    pyg_graphs.append(data)

print(f"Created {len(pyg_graphs)} PyG graph objects with normalized features")

# Split dataset into train and test
split_idx = int(0.8 * len(pyg_graphs))
train_graphs = pyg_graphs[:split_idx]
test_graphs = pyg_graphs[split_idx:]

print(f"Training graphs: {len(train_graphs)}")
print(f"Test graphs: {len(test_graphs)}")

print("\n=== SYSTEMATIC MODEL OPTIMIZATION ===")

# Advanced optimization configurations based on initial results
print("🔬 ADVANCED OPTIMIZATION PHASE")
print("Based on initial results, focusing on promising configurations...")

configs = [
    # Best performing configurations from round 1 with refinements
    {'num_layers': 3, 'dim_h': 64, 'dropout': 0.5, 'lr': 0.01, 'batch_size': 32, 'pooling': 'mean', 'epochs': 150},
    {'num_layers': 3, 'dim_h': 64, 'dropout': 0.4, 'lr': 0.001, 'batch_size': 32, 'pooling': 'mean', 'epochs': 150},
    {'num_layers': 2, 'dim_h': 64, 'dropout': 0.4, 'lr': 0.005, 'batch_size': 32, 'pooling': 'mean', 'epochs': 150},
    {'num_layers': 2, 'dim_h': 96, 'dropout': 0.3, 'lr': 0.001, 'batch_size': 32, 'pooling': 'mean', 'epochs': 150},

    # Smaller models with better regularization
    {'num_layers': 2, 'dim_h': 48, 'dropout': 0.3, 'lr': 0.005, 'batch_size': 16, 'pooling': 'mean', 'epochs': 150},
    {'num_layers': 2, 'dim_h': 80, 'dropout': 0.35, 'lr': 0.003, 'batch_size': 24, 'pooling': 'mean', 'epochs': 150},

    # Deeper but narrower models
    {'num_layers': 4, 'dim_h': 48, 'dropout': 0.4, 'lr': 0.002, 'batch_size': 32, 'pooling': 'mean', 'epochs': 150},
    {'num_layers': 5, 'dim_h': 40, 'dropout': 0.45, 'lr': 0.001, 'batch_size': 32, 'pooling': 'mean', 'epochs': 150},

    # Fine-tuned learning rates with best architectures
    {'num_layers': 3, 'dim_h': 64, 'dropout': 0.4, 'lr': 0.0015, 'batch_size': 32, 'pooling': 'mean', 'epochs': 200},
    {'num_layers': 2, 'dim_h': 64, 'dropout': 0.35, 'lr': 0.0025, 'batch_size': 32, 'pooling': 'mean', 'epochs': 200},

    # Aggressive regularization attempts
    {'num_layers': 3, 'dim_h': 64, 'dropout': 0.6, 'lr': 0.001, 'batch_size': 16, 'pooling': 'mean', 'epochs': 150},
    {'num_layers': 2, 'dim_h': 64, 'dropout': 0.55, 'lr': 0.002, 'batch_size': 24, 'pooling': 'mean', 'epochs': 150},
]

# Run optimization experiments
results = []
best_result = None
best_rmse = float('inf')

print(f"Running {len(configs)} optimization experiments...")

for i, config in enumerate(configs):
    print(f"\n{'='*60}")
    print(f"EXPERIMENT {i+1}/{len(configs)}")
    print(f"{'='*60}")

    try:
        result = train_and_evaluate_model(config)
        results.append(result)

        print(f"Results: RMSE={result['rmse']:.4f}, MAE={result['mae']:.4f}, R²={result['r2']:.4f}")
        print(f"Parameters: {result['parameters']}")

        if result['rmse'] < best_rmse:
            best_rmse = result['rmse']
            best_result = result
            print(f"🎉 NEW BEST MODEL! RMSE: {best_rmse:.4f}")

    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        continue

print("\n" + "="*60)
print("OPTIMIZATION RESULTS SUMMARY")
print("="*60)

if best_result:
    print(f"\n🏆 BEST MODEL CONFIGURATION:")
    config = best_result['config']
    print(f"   • Layers: {config['num_layers']}")
    print(f"   • Hidden dimension: {config['dim_h']}")
    print(f"   • Dropout rate: {config['dropout']}")
    print(f"   • Learning rate: {config['lr']}")
    print(f"   • Batch size: {config['batch_size']}")
    print(f"   • Pooling: {config['pooling']}")
    print(f"   • Parameters: {best_result['parameters']:,}")

    print(f"\n🎯 BEST PERFORMANCE METRICS:")
    print(f"   • Test RMSE: {best_result['rmse']:.4f} eV")
    print(f"   • Test MAE: {best_result['mae']:.4f} eV")
    print(f"   • Test R²: {best_result['r2']:.4f}")
    print(f"   • Test Loss: {best_result['test_loss']:.4f}")

    # Check if we met success criteria
    success_rmse = best_result['rmse'] < 0.5
    success_r2 = best_result['r2'] > 0

    print(f"\n✅ SUCCESS CRITERIA:")
    print(f"   • RMSE < 0.5 eV: {'✓' if success_rmse else '✗'} ({best_result['rmse']:.4f})")
    print(f"   • R² > 0: {'✓' if success_r2 else '✗'} ({best_result['r2']:.4f})")

    if success_rmse and success_r2:
        print(f"\n🎉 ALL SUCCESS CRITERIA MET!")
    else:
        print(f"\n⚠️  Some criteria not met - consider further optimization")

# Show top 5 results
print(f"\n📊 TOP 5 CONFIGURATIONS BY RMSE:")
sorted_results = sorted(results, key=lambda x: x['rmse'])
for i, result in enumerate(sorted_results[:5]):
    config = result['config']
    print(f"{i+1}. RMSE: {result['rmse']:.4f}, R²: {result['r2']:.4f} "
          f"[L:{config['num_layers']}, H:{config['dim_h']}, "
          f"D:{config['dropout']}, LR:{config['lr']}, B:{config['batch_size']}]")

print(f"\n=== FINAL OPTIMIZED MODEL TRAINING ===")
if best_result:
    print("Training final model with best configuration...")
    final_result = train_and_evaluate_model(best_result['config'])

    print(f"\n=== FINAL MODEL EVALUATION ===")
    print(f"Final RMSE: {final_result['rmse']:.4f} eV")
    print(f"Final MAE: {final_result['mae']:.4f} eV")
    print(f"Final R²: {final_result['r2']:.4f}")
    print(f"Model parameters: {final_result['parameters']:,}")

print(f"\n=== DATASET SUMMARY ===")
print(f"Total materials: {len(pyg_graphs)}")
print(f"Node features per atom: {actual_feature_dim}")
print(f"Average nodes per graph: {np.mean([g.x.shape[0] for g in pyg_graphs]):.2f}")
print(f"Average edges per graph: {np.mean([g.edge_index.shape[1] for g in pyg_graphs]):.2f}")
print(f"Bandgap range: {min(y):.3f} - {max(y):.3f} eV")

print(f"\n🚀 GIN MODEL OPTIMIZATION COMPLETED!")
print(f"Tested {len(configs)} different configurations")
print(f"Best RMSE achieved: {best_rmse:.4f} eV")
print("="*60)
