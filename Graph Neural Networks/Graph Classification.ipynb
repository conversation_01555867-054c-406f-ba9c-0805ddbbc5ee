from torch_geometric.datasets import TUDataset


dataset = TUDataset(root='.', name="PROTEINS").shuffle()

print(dataset)
print('Number of graphs:',len(dataset))
print('Number of nodes in the first graph:',dataset[0].x.shape[0])
print('NUmber of features:',dataset.num_features)
print('Number of classes:',dataset.num_classes)

from torch_geometric.loader import DataLoader

train_dataset=dataset[:int(len(dataset)*0.8)]
val_dataset=dataset[int(len(dataset)*0.8):int(len(dataset)*0.9)]
test_dataset=dataset[int(len(dataset)*0.9):]

print(len(train_dataset))
print(len(val_dataset))
print(len(test_dataset))


train_loader=DataLoader(train_dataset,batch_size=32,shuffle=True)
val_loader=DataLoader(val_dataset,batch_size=32,shuffle=True)
test_loader=DataLoader(test_dataset,batch_size=32,shuffle=True)

print('Train loader:')
for i, batch, in enumerate(train_loader):
    print(f'-Batch{i}:{batch}')

print('Validation loader:')
for i, batch in enumerate(val_loader):
    print(f'- Batch{i}:{batch}')

import torch
torch.manual_seed(11)
torch.cuda.manual_seed(0)
torch.cuda.manual_seed_all(0)
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False
import torch.nn.functional as F
from torch.nn import Linear, Sequential, BatchNorm1d, ReLU, Sigmoid, Dropout
from torch_geometric.nn import GINConv
from torch_geometric.nn import global_add_pool

def accuracy(y_pred,y_true):
    return ((y_pred==y_true).sum()/(len(y_true))).item()

import torch
torch.manual_seed(0)
import torch.nn.functional as F
from torch.nn import Linear, Sequential, BatchNorm1d, ReLU, Dropout
from torch_geometric.nn import GCNConv, GINConv
from torch_geometric.nn import global_mean_pool, global_add_pool


class GCN(torch.nn.Module):
    """GCN"""
    def __init__(self, dim_h):
        super(GCN, self).__init__()
        self.conv1 = GCNConv(dataset.num_node_features, dim_h)
        self.conv2 = GCNConv(dim_h, dim_h)
        self.conv3 = GCNConv(dim_h, dim_h)
        self.lin = Linear(dim_h, dataset.num_classes)

    def forward(self, x, edge_index, batch):
        # Node embeddings 
        h = self.conv1(x, edge_index)
        h = h.relu()
        h = self.conv2(h, edge_index)
        h = h.relu()
        h = self.conv3(h, edge_index)

        # Graph-level readout
        hG = global_mean_pool(h, batch)

        # Classifier
        h = F.dropout(hG, p=0.5, training=self.training)
        h = self.lin(h)
        
        return F.log_softmax(h, dim=1)

class GIN(torch.nn.Module):
    """GIN"""
    def __init__(self, dim_h):
        super(GIN, self).__init__()
        self.conv1 = GINConv(
            Sequential(Linear(dataset.num_node_features, dim_h),
                       BatchNorm1d(dim_h), ReLU(),
                       Linear(dim_h, dim_h), ReLU()))
        self.conv2 = GINConv(
            Sequential(Linear(dim_h, dim_h), BatchNorm1d(dim_h), ReLU(),
                       Linear(dim_h, dim_h), ReLU()))
        self.conv3 = GINConv(
            Sequential(Linear(dim_h, dim_h), BatchNorm1d(dim_h), ReLU(),
                       Linear(dim_h, dim_h), ReLU()))
        self.lin1 = Linear(dim_h*3, dim_h*3)
        self.lin2 = Linear(dim_h*3, dataset.num_classes)

    def forward(self, x, edge_index, batch):
        # Node embeddings 
        h1 = self.conv1(x, edge_index)
        h2 = self.conv2(h1, edge_index)
        h3 = self.conv3(h2, edge_index)

        # Graph-level readout
        h1 = global_add_pool(h1, batch)
        h2 = global_add_pool(h2, batch)
        h3 = global_add_pool(h3, batch)

        # Concatenate graph embeddings
        h = torch.cat((h1, h2, h3), dim=1)

        # Classifier
        h = self.lin1(h)
        h = h.relu()
        h = F.dropout(h, p=0.5, training=self.training)
        h = self.lin2(h)
        
        return F.log_softmax(h, dim=1)

def train(model, loader):
 criterion = torch.nn.CrossEntropyLoss()
 optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
 epochs = 100
 model.train()
 for epoch in range(epochs+1):
    total_loss = 0
    acc = 0
    val_loss = 0
    val_acc = 0
    # Train on batches
    for data in loader:
        optimizer.zero_grad()
        out=model(data.x,data.edge_index,data.batch)
        loss = criterion(out, data.y)
        total_loss += loss / len(loader)
        acc += accuracy(out.argmax(dim=1), data.y) / len(loader)
        loss.backward()
        optimizer.step()
 # Validation
    val_loss, val_acc = test(model, val_loader)
    if(epoch % 20 == 0):
        print(f'Epoch {epoch:>3} | Train Loss: {total_loss:.2f} | Train Acc: {acc*100:>5.2f}% | Val\
        Loss: {val_loss:.2f} | Val Acc: {val_acc*100:.2f}%')
 return model

@torch.no_grad()
def test(model, loader):
    criterion = torch.nn.CrossEntropyLoss()
    model.eval()
    loss = 0
    acc = 0

    for data in loader:
        out = model(data.x, data.edge_index, data.batch)
        loss += criterion(out, data.y) / len(loader)
        acc += accuracy(out.argmax(dim=1), data.y) / len(loader)

    return loss, acc

gin = GIN(dim_h=32)
gin = train(gin, train_loader)

api = 'h79cBkPf4hpT24odYyHNpGTRNWuuPh2E'
from mp_api.client import MPRester
from pymatgen.analysis.local_env import CrystalNN
import numpy as np

with MPRester(api) as mpr:
    structure = mpr.get_structure_by_material_id("mp-1244")
    cnn = CrystalNN()
    n_sites = len(structure)
    
    # Initialize adjacency matrix
    adjacency_matrix = np.zeros((n_sites, n_sites), dtype=int)
    
    # Fill adjacency matrix
    for i in range(n_sites):
        neighbors = cnn.get_nn_info(structure, i)
        for neighbor in neighbors:
            j = neighbor['site_index']
            adjacency_matrix[i, j] = 1
    
    # Get elements in order of sites (index order)
    elements_in_order = [site.species_string for site in structure]

print(adjacency_matrix)
from torch_geometric.utils import dense_to_sparse
edge_index, edge_attr = dense_to_sparse(torch.from_numpy(adjacency_matrix))
print(edge_index)


edge_attr

