import torch
import torch.nn.functional as F
from torch_geometric.datasets import Planetoid

dataset = Planetoid(root='.', name="Pubmed")
data = dataset[0]


print('Number of graphs:',len(dataset))
print('Number of nodes:',data.x.shape[0])
print('number of features:',dataset.num_features)
print('Number of classes:', dataset.num_classes)

from torch_geometric.loader import NeighborLoader
from torch_geometric.utils import to_networkx

# Create batches with neighbor sampling
train_loader = NeighborLoader(
    data,
    num_neighbors=[5, 10],
    batch_size=16,
    input_nodes=data.train_mask,
)

# Print each subgraph
for i, subgraph in enumerate(train_loader):
    print(f'Subgraph {i}: {subgraph}')

import numpy as np
import networkx as nx
import matplotlib.pyplot as plt

fig = plt.figure(figsize=(16,16))
for idx, (subdata, pos) in enumerate(zip(train_loader, [221, 222, 223, 224])):
    G = to_networkx(subdata, to_undirected=True)
    ax = fig.add_subplot(pos)
    ax.set_title(f'Subgraph {idx}', fontsize=24)
    plt.axis('off')
    nx.draw_networkx(G,
                    pos=nx.spring_layout(G, seed=0),
                    with_labels=False,
                    node_color=subdata.y,
                    )
plt.show()

def accuracy(pred_y, y):
 return ((pred_y == y).sum() / len(y)).item()

import torch
from torch.nn import functional as F
from torch_geometric.nn import SAGEConv

class GraphSAGE(torch.nn.Module):
    def __init__(self,dim_in,dim_h,dim_out):
        super().__init__()
        self.sage1=SAGEConv(dim_in,dim_h)
        self.sage2=SAGEConv(dim_h,dim_out)

    def forward(self,x,edge_index):
        h=self.sage1(x,edge_index)
        h=torch.relu(h)
        h=F.dropout(h,p=0.5,training=self.training)
        h=self.sage2(h,edge_index)
        return F.log_softmax(h,dim=1)

    def fit(self,data,epochs):
        criterion=torch.nn.CrossEntropyLoss()
        optimizer=torch.optim.Adam(self.parameters(),lr=0.01)
        self.train()
        for epoch in range(epochs+1):
            total_loss,val_loss,acc,val_acc=0,0,0,0
            for batch in train_loader:
                optimizer.zero_grad()
                out=self(batch.x,batch.edge_index)
                loss=criterion(out[batch.train_mask],batch.y[batch.train_mask])
                total_loss+=loss
                acc += accuracy(out[batch.train_mask].argmax(dim=1), batch.y[batch.train_mask])                
                loss.backward()
                optimizer.step()
                val_loss+=criterion(out[batch.val_mask],batch.y[batch.val_mask])
                val_acc += accuracy(out[batch.val_mask].argmax(dim=1), batch.y[batch.val_mask])
            if epoch % 20 == 0:
                print(f'Epoch {epoch:>3} | Train Loss: {loss/len(train_loader):.3f} | '
                    f'Train Acc: {acc/len(train_loader)*100:>6.2f}% | '
                    f'Val Loss: {val_loss/len(train_loader):.2f} | '
                    f'Val Acc: {val_acc/len(train_loader)*100:.2f}%')
        
    @torch.no_grad()
    def test(self, data):
        self.eval()
        out = self(data.x, data.edge_index)
        acc = accuracy(out.argmax(dim=1)[data.test_mask], 
        data.y[data.test_mask])
        return acc


graphsage = GraphSAGE(dataset.num_features, 64, dataset.
num_classes)
print(graphsage)
graphsage.fit(data, 200)

acc = graphsage.test(data)
print(f'GraphSAGE test accuracy: {acc*100:.2f}%')


